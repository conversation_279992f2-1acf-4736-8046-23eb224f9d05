'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { 
  Bars3Icon, 
  XMarkIcon, 
  ChevronDownIcon,
  HomeIcon,
  HeartIcon,
  AcademicCapIcon,
  BookOpenIcon,
  UserIcon,
  PhoneIcon,
  MapIcon,
  CalendarDaysIcon,
  GlobeAltIcon,
  CameraIcon,
  InformationCircleIcon,
  ChatBubbleBottomCenterTextIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';
import { useInView } from 'react-intersection-observer';

interface NavItem {
  label: string;
  href: string;
  icon: React.ComponentType<any>;
  isHighlighted?: boolean;
  description?: string;
  badge?: string;
  subItems?: Array<{
    label: string;
    href: string;
    icon: React.ComponentType<any>;
    description?: string;
  }>;
}

const navigationItems: NavItem[] = [
  {
    label: '<PERSON>rona Główna',
    href: '/',
    icon: HomeIcon,
    description: 'Odk<PERSON>j transformacyjne podróże'
  },
  {
    label: 'Retreaty',
    href: '/retreaty',
    icon: HeartIcon,
    description: 'Nasze oferty na 2025',
    badge: '2025',
    subItems: [
      {
        label: 'Bali - Ubud & Gili Air',
        href: '/retreaty-jogi-bali-2025',
        icon: MapIcon,
        description: 'Klasyczny program 7-14 dni'
      },
      {
        label: 'Sri Lanka - Sigiriya',
        href: '/joga-sri-lanka-retreat',
        icon: MapIcon,
        description: 'Duchowa podróż 10 dni'
      },
      {
        label: 'Kalendarz Terminów',
        href: '/kalendarz-retreatow',
        icon: CalendarDaysIcon,
        description: 'Dostępne daty w 2025'
      }
    ]
  },
  {
    label: 'Zajęcia Online',
    href: '/zajecia-online',
    icon: AcademicCapIcon,
    description: 'Joga z domu',
    isHighlighted: true
  },
  {
    label: 'Blog',
    href: '/blog',
    icon: BookOpenIcon,
    description: 'Inspiracje i przewodniki',
    subItems: [
      {
        label: 'Przewodniki Podróży',
        href: '/blog?category=przewodniki',
        icon: GlobeAltIcon,
        description: 'Praktyczne porady'
      },
      {
        label: 'Galeria Zdjęć',
        href: '/galeria',
        icon: CameraIcon,
        description: 'Wspomnienia z podróży'
      }
    ]
  },
  {
    label: 'O Mnie',
    href: '/julia-jakubowicz-instruktor',
    icon: UserIcon,
    description: 'Julia Jakubowicz - instruktorka'
  },
  {
    label: 'Kontakt',
    href: '/kontakt',
    icon: PhoneIcon,
    description: 'Porozmawiajmy o Twoim retreat'
  }
];

const footerItems = [
  {
    label: 'Mapa Strony',
    href: '/mapa',
    icon: MapIcon
  },
  {
    label: 'Polityka Prywatności',
    href: '/polityka-prywatnosci',
    icon: ShieldCheckIcon
  },
  {
    label: 'FAQ',
    href: '/faq',
    icon: InformationCircleIcon
  },
  {
    label: 'Recenzje',
    href: '/opinie',
    icon: ChatBubbleBottomCenterTextIcon
  }
];

const EnterpriseNavbar: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const menuRef = useRef<HTMLDivElement>(null);
  const [heroRef, heroInView] = useInView({ threshold: 0.1 });

  useEffect(() => {
    setMounted(true);
    
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setScrolled(scrollPosition > 50);
      setShowBackToTop(scrollPosition > 500);
    };

    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMenuOpen(false);
        setActiveSubmenu(null);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  const isActiveLink = (href: string) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  };

  const handleMenuToggle = () => {
    setIsMenuOpen(!isMenuOpen);
    setActiveSubmenu(null);
  };

  const handleSubmenuToggle = (href: string) => {
    setActiveSubmenu(activeSubmenu === href ? null : href);
  };

  const handleNavigation = (href: string) => {
    setIsMenuOpen(false);
    setActiveSubmenu(null);
    router.push(href);
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  if (!mounted) return null;

  return (
    <>
      {/* Hero Section Reference */}
      <div ref={heroRef} className="absolute top-0 left-0 w-full h-1 pointer-events-none" />
      
      {/* Navigation Header */}
      <motion.nav
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: 'easeOut' }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          scrolled || !heroInView
            ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-enterprise-brown/10' 
            : 'bg-transparent'
        }`}
      >
        <div className="container mx-auto px-4 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link 
              href="/" 
              className="group relative z-10"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
                className="font-cormorant text-xl lg:text-2xl font-light text-charcoal tracking-[0.12em] transition-all duration-300 group-hover:text-enterprise-brown"
              >
                BAKASANA
                <div className="absolute bottom-0 left-0 w-0 h-px bg-enterprise-brown transition-all duration-300 group-hover:w-full" />
              </motion.div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navigationItems.slice(1).map((item, index) => (
                <div key={item.href} className="relative group">
                  <Link
                    href={item.href}
                    className={`flex items-center gap-2 px-3 py-2 text-sm font-medium tracking-wide uppercase transition-all duration-300 ${
                      isActiveLink(item.href)
                        ? 'text-enterprise-brown'
                        : 'text-charcoal hover:text-enterprise-brown hover:-translate-y-0.5'
                    } ${
                      item.isHighlighted 
                        ? 'border border-enterprise-brown rounded-none hover:bg-enterprise-brown hover:text-white' 
                        : ''
                    }`}
                  >
                    <item.icon className="w-4 h-4" />
                    {item.label}
                    {item.badge && (
                      <span className="px-2 py-1 text-xs bg-enterprise-brown text-white rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                  
                  {!item.isHighlighted && (
                    <div className={`absolute bottom-0 left-0 h-px bg-enterprise-brown transition-all duration-300 ${
                      isActiveLink(item.href) ? 'w-full' : 'w-0 group-hover:w-full'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            {/* Mobile Menu Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleMenuToggle}
              className="lg:hidden relative z-10 flex items-center justify-center w-10 h-10 rounded-none bg-transparent border border-charcoal/20 hover:border-enterprise-brown transition-all duration-300"
              aria-label="Toggle navigation menu"
            >
              <AnimatePresence mode="wait">
                {isMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <XMarkIcon className="w-6 h-6 text-charcoal" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Bars3Icon className="w-6 h-6 text-charcoal" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={() => setIsMenuOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Mobile Menu Panel */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            ref={menuRef}
            initial={{ x: '100%', opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: '100%', opacity: 0 }}
            transition={{ type: 'tween', duration: 0.3, ease: 'easeInOut' }}
            className="fixed right-0 top-0 h-full w-full max-w-sm bg-white z-50 shadow-2xl overflow-y-auto"
          >
            <div className="p-6 pt-24">
              {/* Main Navigation */}
              <div className="space-y-4">
                {navigationItems.map((item, index) => (
                  <motion.div
                    key={item.href}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => handleNavigation(item.href)}
                        className={`flex items-center gap-3 w-full text-left p-3 rounded-none transition-all duration-300 group ${
                          isActiveLink(item.href)
                            ? 'bg-enterprise-brown/10 text-enterprise-brown border-l-4 border-enterprise-brown'
                            : 'hover:bg-gray-50 text-charcoal hover:text-enterprise-brown'
                        } ${
                          item.isHighlighted 
                            ? 'border border-enterprise-brown bg-enterprise-brown/5' 
                            : ''
                        }`}
                      >
                        <item.icon className="w-5 h-5 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">{item.label}</span>
                            {item.badge && (
                              <span className="px-2 py-1 text-xs bg-enterprise-brown text-white rounded-full">
                                {item.badge}
                              </span>
                            )}
                          </div>
                          {item.description && (
                            <p className="text-xs text-gray-500 mt-1">{item.description}</p>
                          )}
                        </div>
                      </button>
                      
                      {item.subItems && (
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => handleSubmenuToggle(item.href)}
                          className="p-2 rounded-none hover:bg-gray-100 transition-colors"
                        >
                          <motion.div
                            animate={{ rotate: activeSubmenu === item.href ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                          </motion.div>
                        </motion.button>
                      )}
                    </div>

                    {/* Submenu */}
                    <AnimatePresence>
                      {item.subItems && activeSubmenu === item.href && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="overflow-hidden"
                        >
                          <div className="ml-8 space-y-2 border-l-2 border-gray-100 pl-4">
                            {item.subItems.map((subItem, subIndex) => (
                              <motion.button
                                key={subItem.href}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: subIndex * 0.05, duration: 0.2 }}
                                onClick={() => handleNavigation(subItem.href)}
                                className={`flex items-center gap-3 w-full text-left p-2 rounded-none transition-all duration-300 ${
                                  isActiveLink(subItem.href)
                                    ? 'bg-enterprise-brown/10 text-enterprise-brown'
                                    : 'hover:bg-gray-50 text-gray-600 hover:text-enterprise-brown'
                                }`}
                              >
                                <subItem.icon className="w-4 h-4 flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                  <span className="font-medium text-sm">{subItem.label}</span>
                                  {subItem.description && (
                                    <p className="text-xs text-gray-500 mt-1">{subItem.description}</p>
                                  )}
                                </div>
                              </motion.button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </div>

              {/* Divider */}
              <div className="my-8 border-t border-gray-200" />

              {/* Footer Links */}
              <div className="space-y-3">
                <h3 className="font-medium text-xs uppercase tracking-wide text-gray-500 mb-4">
                  Dodatkowe Informacje
                </h3>
                {footerItems.map((item, index) => (
                  <motion.button
                    key={item.href}
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 + index * 0.05, duration: 0.2 }}
                    onClick={() => handleNavigation(item.href)}
                    className="flex items-center gap-3 w-full text-left p-2 rounded-none hover:bg-gray-50 text-gray-600 hover:text-enterprise-brown transition-all duration-300"
                  >
                    <item.icon className="w-4 h-4 flex-shrink-0" />
                    <span className="text-sm">{item.label}</span>
                  </motion.button>
                ))}
              </div>

              {/* Contact CTA */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.3 }}
                className="mt-8 p-4 bg-enterprise-brown/5 border border-enterprise-brown/20 rounded-none"
              >
                <h3 className="font-medium text-sm text-charcoal mb-2">
                  Potrzebujesz pomocy?
                </h3>
                <p className="text-xs text-gray-600 mb-3">
                  Skontaktuj się z nami, aby uzyskać więcej informacji o retreatach.
                </p>
                <button
                  onClick={() => handleNavigation('/kontakt')}
                  className="w-full bg-enterprise-brown text-white py-2 px-4 rounded-none hover:bg-enterprise-brown/90 transition-colors text-sm font-medium"
                >
                  Skontaktuj się z nami
                </button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Back to Top Button */}
      <AnimatePresence>
        {showBackToTop && (
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={scrollToTop}
            className="fixed bottom-6 right-6 z-40 p-3 bg-enterprise-brown text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
            aria-label="Scroll to top"
          >
            <ArrowUpIcon className="w-5 h-5" />
          </motion.button>
        )}
      </AnimatePresence>
    </>
  );
};

export default EnterpriseNavbar;