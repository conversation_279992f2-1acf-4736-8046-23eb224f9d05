import React from 'react';
import { generateMetadata as generateSEOMetadata } from '../metadata';
import BookingCalendar from '@/components/BookingCalendar';
import FAQSection, { bookingFAQs } from '@/components/FAQSection';
import PerformantWhatsApp from '@/components/PerformantWhatsApp';
import FormPreconnect from '@/components/FormPreconnect';

export const metadata = generateSEOMetadata({
  title: 'Rezerwacja Retreatu - Kalendarz Dostępności',
  description: 'Sprawdź dostępne terminy retreatów jogowych na Bali i Sri Lance. Zarezerwuj swoje miejsce na niezapomnianej przygodzie.',
  keywords: ['rezerwacja retreat', 'kalendarz', 'dostępność', 'booking', 'retreat jogowy Bali'],
});

export default function RezerwacjaPage() {
  return (
    <>
      <FormPreconnect />
      <main className="py-20 min-h-screen">
      <div className="max-w-7xl mx-auto container-padding">
        <header className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-serif text-temple mb-6 font-light">
            Rezerwacja <span className="gradient-text">Retreatu</span>
          </h1>
          <p className="text-lg text-wood-light max-w-3xl mx-auto leading-relaxed mb-8 font-light">
            Wybierz termin, który Ci odpowiada i zarezerwuj swoje miejsce na niezapomnianej 
            przygodzie jogowej na Bali lub Sri Lance.
          </p>
          <div className="decorative-line" />
        </header>

        <BookingCalendar />

        {/* Additional Info Section */}
        <section className="mt-20 grid md:grid-cols-2 gap-12">
          <div className="bg-temple/5 p-8 rounded-2xl">
            <h3 className="text-xl font-serif text-temple mb-4">
              Jak przebiega rezerwacja?
            </h3>
            <div className="space-y-4 text-wood-light">
              <div className="flex items-start gap-3">
                <span className="bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">1</span>
                <div>
                  <strong>Wybierz termin</strong> - Kliknij na wybrany retreat w kalendarzu
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">2</span>
                <div>
                  <strong>Wypełnij formularz</strong> - Podaj dane osobowe i preferencje
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">3</span>
                <div>
                  <strong>Wpłać zadatek</strong> - 30% wartości retreatu (dane w emailu)
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">4</span>
                <div>
                  <strong>Otrzymaj potwierdzenie</strong> - Email z wszystkimi szczegółami
                </div>
              </div>
            </div>
          </div>

          <div className="bg-golden/5 p-8 rounded-2xl">
            <h3 className="text-xl font-serif text-temple mb-4">
              Co jest wliczone w cenę?
            </h3>
            <div className="space-y-3 text-wood-light">
              <div className="flex items-center gap-3">
                <span className="text-temple">✓</span>
                <span>7 nocy w hotelu (pokój dzielony)</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-temple">✓</span>
                <span>Wszystkie posiłki (śniadanie, lunch, kolacja)</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-temple">✓</span>
                <span>Transport lotnisko-hotel-lotnisko</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-temple">✓</span>
                <span>Praktyka jogi 2x dziennie</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-temple">✓</span>
                <span>Medytacje i warsztaty</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-temple">✓</span>
                <span>Zwiedzanie i wycieczki</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-temple">✓</span>
                <span>Opieka instruktora przez cały pobyt</span>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-temple/10">
              <h4 className="font-medium text-temple mb-2">Dodatkowo płatne:</h4>
              <div className="space-y-2 text-sm text-wood-light">
                <div>• Pokój jednoosobowy: +500 PLN</div>
                <div>• Bilety lotnicze (pomoc w organizacji)</div>
                <div>• Ubezpieczenie podróżne</div>
                <div>• Wydatki osobiste i pamiątki</div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section with Structured Data */}
        <FAQSection
          faqs={bookingFAQs}
          title="Często zadawane pytania o rezerwację"
          subtitle="Znajdź odpowiedzi na pytania dotyczące procesu rezerwacji i płatności"
          className="mt-20"
        />

        {/* Contact CTA */}
        <section className="mt-20 text-center">
          <div className="bg-gradient-to-r from-temple/5 to-golden/5 rounded-2xl p-12">
            <h3 className="text-2xl font-serif text-temple mb-4">
              Masz pytania?
            </h3>
            <p className="text-wood-light mb-8 max-w-2xl mx-auto">
              Jeśli nie znalazłeś odpowiedzi na swoje pytanie lub potrzebujesz pomocy 
              z rezerwacją, skontaktuj się z nami bezpośrednio.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="flex justify-center">
                <PerformantWhatsApp 
                  size="md"
                  variant="button"
                  className="px-8 py-3 rounded-lg"
                  message="Cześć Julia! Mam pytania dotyczące rezerwacji retreatu. Czy możesz mi pomóc?"
                />
              </div>
              <a
                href="mailto:<EMAIL>"
                className="btn-unified-secondary"
              >
                Napisz email
              </a>
              <a
                href="tel:+48606101523"
                className="btn-unified-secondary"
              >
                Zadzwoń: +48 606 101 523
              </a>
            </div>
          </div>
        </section>
      </div>

      {/* Floating WhatsApp */}
      <div className="fixed bottom-6 right-6 z-50">
        <PerformantWhatsApp
          size="lg"
          message="Cześć Julia! Chciałabym/chciałbym zarezerwować miejsce na retreatie. Jakie są dostępne terminy?"
        />
      </div>
    </main>
    </>
  );
}
