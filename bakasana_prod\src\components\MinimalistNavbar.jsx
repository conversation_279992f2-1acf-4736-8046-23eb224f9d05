'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function MinimalistNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActiveLink = (href) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      scrolled 
        ? 'bg-white/85 backdrop-blur-[10px] shadow-sm' 
        : 'bg-transparent'
    }`}>
      <div className="flex items-center justify-between px-8 py-6">
        {/* Logo po lewej stronie */}
        <Link 
          href="/" 
          className="text-enterprise-brown font-cormorant text-lg font-normal tracking-[0.5px] transition-all duration-300 hover:opacity-70"
        >
          BAKASANA
        </Link>

        {/* Menu po prawej stronie */}
        <ul className="flex items-center space-x-12">
          <li>
            <Link 
              href="/retreaty" 
              className={`text-[13px] font-normal tracking-[1.5px] transition-all duration-300 relative ${
                isActiveLink('/retreaty') 
                  ? 'text-enterprise-brown' 
                  : 'text-charcoal-light hover:text-enterprise-brown'
              }`}
            >
              RETREATY
              {isActiveLink('/retreaty') && (
                <span className="absolute -bottom-1 left-0 w-full h-px bg-enterprise-brown animate-fade-in"></span>
              )}
            </Link>
          </li>
          <li>
            <Link 
              href="/zajecia-online" 
              className={`text-[13px] font-normal tracking-[1.5px] transition-all duration-300 relative ${
                isActiveLink('/zajecia-online') 
                  ? 'text-enterprise-brown' 
                  : 'text-charcoal-light hover:text-enterprise-brown'
              }`}
            >
              ZAJĘCIA ONLINE
              {isActiveLink('/zajecia-online') && (
                <span className="absolute -bottom-1 left-0 w-full h-px bg-enterprise-brown animate-fade-in"></span>
              )}
            </Link>
          </li>
          <li>
            <Link 
              href="/blog" 
              className={`text-[13px] font-normal tracking-[1.5px] transition-all duration-300 relative ${
                isActiveLink('/blog') 
                  ? 'text-enterprise-brown' 
                  : 'text-charcoal-light hover:text-enterprise-brown'
              }`}
            >
              BLOG
              {isActiveLink('/blog') && (
                <span className="absolute -bottom-1 left-0 w-full h-px bg-enterprise-brown animate-fade-in"></span>
              )}
            </Link>
          </li>
          <li>
            <Link 
              href="/o-mnie" 
              className={`text-[13px] font-normal tracking-[1.5px] transition-all duration-300 relative ${
                isActiveLink('/o-mnie') 
                  ? 'text-enterprise-brown' 
                  : 'text-charcoal-light hover:text-enterprise-brown'
              }`}
            >
              O MNIE
              {isActiveLink('/o-mnie') && (
                <span className="absolute -bottom-1 left-0 w-full h-px bg-enterprise-brown animate-fade-in"></span>
              )}
            </Link>
          </li>
          <li>
            <Link 
              href="/kontakt" 
              className={`text-[13px] font-normal tracking-[1.5px] transition-all duration-300 relative ${
                isActiveLink('/kontakt') 
                  ? 'text-enterprise-brown' 
                  : 'text-charcoal-light hover:text-enterprise-brown'
              }`}
            >
              KONTAKT
              {isActiveLink('/kontakt') && (
                <span className="absolute -bottom-1 left-0 w-full h-px bg-enterprise-brown animate-fade-in"></span>
              )}
            </Link>
          </li>
        </ul>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; width: 0; }
          to { opacity: 1; width: 100%; }
        }
        .animate-fade-in {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>
    </nav>
  );
}