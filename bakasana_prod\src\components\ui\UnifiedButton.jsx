'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedButton - Ujednolicony system przycisków BAKASANA
 * Elegancja Old Money + Ciepły minimalizm
 */

const buttonVariants = {
  // PRIMARY - Gł<PERSON> akcje (CTA)
  primary: {
    base: "bg-enterprise-brown text-sanctuary border border-enterprise-brown",
    hover: "hover:bg-terra hover:border-terra hover:shadow-elegant",
    focus: "focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2"
  },
  
  // SECONDARY - Drugie w hierarchii
  secondary: {
    base: "bg-transparent text-enterprise-brown border border-enterprise-brown",
    hover: "hover:bg-enterprise-brown hover:text-sanctuary hover:shadow-elegant",
    focus: "focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2"
  },
  
  // GHOST - Subtelne akcje
  ghost: {
    base: "bg-transparent text-charcoal border-0",
    hover: "hover:bg-whisper hover:text-enterprise-brown",
    focus: "focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1"
  },
  
  // MINIMAL - Ultra-subtelne
  minimal: {
    base: "bg-transparent text-sage border-0 underline decoration-1 underline-offset-4",
    hover: "hover:text-enterprise-brown hover:decoration-enterprise-brown",
    focus: "focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1"
  }
};

const sizeVariants = {
  sm: "px-6 py-2 text-xs tracking-[1px]",
  md: "px-8 py-3 text-sm tracking-[1.2px]", 
  lg: "px-12 py-4 text-sm tracking-[1.5px]",
  xl: "px-16 py-5 text-base tracking-[2px]"
};

export default function UnifiedButton({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  loading = false,
  as: Component = 'button',
  ...props
}) {
  const variantStyles = buttonVariants[variant];
  const sizeStyles = sizeVariants[size];
  
  return (
    <Component
      className={cn(
        // Base styles - Old Money elegance
        "inline-flex items-center justify-center font-inter font-light uppercase",
        "transition-all duration-300 ease-out",
        "focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed",
        "transform hover:-translate-y-0.5 active:translate-y-0",
        
        // Variant styles
        variantStyles.base,
        variantStyles.hover,
        variantStyles.focus,
        
        // Size styles
        sizeStyles,
        
        // Loading state
        loading && "opacity-70 cursor-wait",
        
        // Custom className
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg 
          className="animate-spin -ml-1 mr-2 h-4 w-4" 
          fill="none" 
          viewBox="0 0 24 24"
        >
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="2"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </Component>
  );
}

// Wyspecjalizowane warianty dla częstych przypadków użycia

export function CTAButton({ children, ...props }) {
  return (
    <UnifiedButton variant="primary" size="lg" {...props}>
      {children}
    </UnifiedButton>
  );
}

export function SecondaryButton({ children, ...props }) {
  return (
    <UnifiedButton variant="secondary" size="md" {...props}>
      {children}
    </UnifiedButton>
  );
}

export function GhostButton({ children, ...props }) {
  return (
    <UnifiedButton variant="ghost" size="md" {...props}>
      {children}
    </UnifiedButton>
  );
}

export function LinkButton({ children, ...props }) {
  return (
    <UnifiedButton variant="minimal" size="sm" {...props}>
      {children}
    </UnifiedButton>
  );
}