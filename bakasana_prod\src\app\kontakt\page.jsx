import React from 'react';
import { Suspense } from 'react';
import ContactForm from './ContactForm';
import PerformantWhatsApp from '@/components/PerformantWhatsApp';
import FormPreconnect from '@/components/FormPreconnect';

// Metadane są w osobnym pliku metadata.js

export default function KontaktPage() {
  return (
    <>
      <FormPreconnect />
      <div className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Magazine Style Header */}
      <section className="magazine-hero">
        <div className="magazine-hero-content">
          <div className="magazine-header-line"></div>
          
          <h1 className="magazine-title">
            Kontakt
          </h1>

          <p className="magazine-subtitle">
            Zacznij swoją podróż do siebie
          </p>

          <div className="magazine-meta">
            Bali • Sri Lanka • Duchowa Transformacja
          </div>
          
          <div className="magazine-header-line"></div>
        </div>
      </section>

      {/* INSPIRATIONAL SECTION */}
      <section className="container">
        <div className="text-center mb-20">
          <div className="section-divider mb-12"></div>
          <h2 className="section-header mb-8">
            Porozmawiajmy o Twojej podróży
          </h2>
          <p className="body-text max-w-3xl mx-auto mb-8 opacity-80">
            Każda transformacja zaczyna się od pierwszego kroku. Napisz do mnie, a wspólnie
            znajdziemy idealny retreat, który otworzy przed Tobą drzwi do wewnętrznego spokoju
            i odkrycia prawdziwej siebie.
          </p>
          
          {/* Sacred Quote */}
          <div className="flex items-center justify-center my-12">
            <div className="flex items-center gap-4 text-temple-gold/60">
              <div className="w-12 h-px bg-temple-gold/30"></div>
              <span className="text-xl opacity-60">ॐ</span>
              <div className="w-12 h-px bg-temple-gold/30"></div>
            </div>
          </div>
          
          <p className="body-text italic text-temple-gold/80 mb-8">
            "Najdłuższa podróż zaczyna się od jednego kroku"
          </p>
        </div>

        {/* CONTACT OPTIONS */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-20 max-w-4xl mx-auto">
          <div className="text-center p-8">
            <h3 className="font-light text-charcoal mb-4 tracking-wide">WhatsApp</h3>
            <p className="text-sm text-stone font-light mb-4">Szybki kontakt</p>
            <div className="flex justify-center">
              <PerformantWhatsApp 
                size="md"
                variant="button"
                className="px-6 py-3 text-center rounded-lg"
              />
            </div>
          </div>

          <div className="text-center p-8">
            <h3 className="font-light text-charcoal mb-4 tracking-wide">Instagram</h3>
            <p className="text-sm text-stone font-light mb-4">Codzienne inspiracje</p>
            <a
              href="https://www.instagram.com/fly_with_bakasana"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-ghost"
            >
              Obserwuj
            </a>
          </div>

          <div className="text-center p-8">
            <h3 className="font-light text-charcoal mb-4 tracking-wide">Email</h3>
            <p className="text-sm text-stone font-light mb-4">Szczegółowe zapytania</p>
            <a
              href="mailto:<EMAIL>"
              className="btn-ghost"
            >
              Napisz email
            </a>
          </div>
        </div>

        {/* CONTACT FORM SECTION */}
        <div id="contact-form" className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h3 className="section-header mb-8">
              Formularz kontaktowy
            </h3>
            <p className="body-text opacity-80">
              Wypełnij formularz, a odpowiem w ciągu 24 godzin
            </p>
          </div>

          <Suspense fallback={<div className="flex items-center justify-center h-[40vh] text-stone">Ładowanie...</div>}>
            <ContactForm />
          </Suspense>
        </div>
      </section>

      {/* Floating WhatsApp */}
      <div className="fixed bottom-6 right-6 z-50">
        <PerformantWhatsApp
          size="lg"
          message="Cześć Julia! Chciałabym/chciałbym porozmawiać o retreatach. Czy możemy się skontaktować?"
        />
      </div>
    </div>
    </>
  );
}