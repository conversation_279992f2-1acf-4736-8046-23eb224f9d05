'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowDownTrayIcon, 
  XMarkIcon, 
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  ShareIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  StarIcon,
  SparklesIcon,
  BoltIcon,
  WifiIcon,
  ShieldCheckIcon,
  HeartIcon,
  PlayIcon,
  PauseIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  EyeIcon,
  DeviceTabletIcon,
  GlobeAltIcon,
  MapIcon,
  CameraIcon,
  UserIcon,
  BookOpenIcon,
  PhoneIcon,
  HomeIcon,
  AcademicCapIcon,
  ClockIcon,
  RocketLaunchIcon,
  TrophyIcon,
  FireIcon,
  MegaphoneIcon,
  LightBulbIcon,
  HandRaisedIcon,
  FaceSmileIcon,
  GiftIcon,
  CursorArrowRaysIcon,
  BeakerIcon,
  Cog6ToothIcon,
  DocumentTextIcon,
  ArchiveBoxIcon,
  CloudIcon,
  ServerIcon,
  CpuChipIcon,
  SignalIcon,
  BellIcon,
  ChatBubbleBottomCenterTextIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  EnvelopeIcon,
  CalendarDaysIcon,
  TagIcon,
  LinkIcon,
  PhotoIcon,
  VideoCameraIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  PresentationChartBarIcon,
  TableCellsIcon,
  ListBulletIcon,
  Squares2X2Icon,
  ViewColumnsIcon,
  FolderIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  PrinterIcon,
  ArrowUpTrayIcon,
  PaperAirplaneIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  MinusIcon,
  ArrowPathIcon,
  StopIcon,
  ForwardIcon,
  BackwardIcon,
  SunIcon,
  MoonIcon,
  LanguageIcon,
  FlagIcon,
  MapPinIcon,
  TruckIcon,
  CreditCardIcon,
  BanknotesIcon,
  ShoppingBagIcon,
  QrCodeIcon,
  TicketIcon,
  KeyIcon,
  LockClosedIcon,
  EyeSlashIcon,
  FingerPrintIcon,
  IdentificationIcon,
  WalletIcon,
  ScaleIcon,
  CalculatorIcon,
  ClipboardDocumentIcon,
  FolderOpenIcon,
  NewspaperIcon,
  BookmarkIcon,
  RssIcon,
  HashtagIcon,
  AtSymbolIcon,
  CommandLineIcon,
  CodeBracketIcon,
  CubeIcon,
  PuzzlePieceIcon,
  WrenchScrewdriverIcon,
  PaintBrushIcon,
  SwatchIcon,
  MusicalNoteIcon,
  FilmIcon,
  TvIcon,
  RadioIcon,
  PhoneArrowUpRightIcon,
  PhoneArrowDownLeftIcon,
  ChatBubbleLeftRightIcon,
  InboxIcon,
  PaperClipIcon,
  DocumentArrowUpIcon,
  DocumentArrowDownIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ChartBarIcon,
  ChartPieIcon,
  PresentationChartLineIcon,
  AdjustmentsVerticalIcon,
  FunnelIcon,
  MagnifyingGlassCircleIcon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  ViewfinderCircleIcon,
  CameraIcon as CameraIconOutline,
  VideoCameraIcon as VideoCameraIconOutline,
  PhotoIcon as PhotoIconOutline,
  FilmIcon as FilmIconOutline,
  MicrophoneIcon as MicrophoneIconOutline,
  SpeakerWaveIcon as SpeakerWaveIconOutline,
  SpeakerXMarkIcon,
  SpeakerWaveIcon as VolumeUpIcon,
  SpeakerXMarkIcon as VolumeXIcon,
  PlayCircleIcon,
  PauseCircleIcon,
  StopCircleIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  ArrowsRightLeftIcon,
  ArrowsUpDownIcon,
  ArrowsPointingInIcon,
  ArrowsPointingOutIcon,
  MagnifyingGlassIcon as MagnifyingGlassIconOutline,
  AdjustmentsHorizontalIcon as AdjustmentsHorizontalIconOutline
} from '@heroicons/react/24/outline';

interface PWAScreenshot {
  src: string;
  sizes: string;
  type: string;
  form_factor: 'narrow' | 'wide';
  label: string;
}

interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop';
  os: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown';
  browser: 'chrome' | 'firefox' | 'safari' | 'edge' | 'unknown';
  capabilities: string[];
}

interface PWAFeature {
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  category: 'performance' | 'offline' | 'native' | 'security' | 'engagement';
}

const PWA_FEATURES: PWAFeature[] = [
  {
    icon: BoltIcon,
    title: 'Lightning Fast',
    description: 'Native app performance with instant loading and smooth animations',
    category: 'performance'
  },
  {
    icon: WifiIcon,
    title: 'Offline Access',
    description: 'Access your travel guides and retreat information even without internet',
    category: 'offline'
  },
  {
    icon: BellIcon,
    title: 'Push Notifications',
    description: 'Get notified about new retreats, booking confirmations, and travel updates',
    category: 'engagement'
  },
  {
    icon: HomeIcon,
    title: 'Home Screen Access',
    description: 'Add to your home screen for quick access like a native app',
    category: 'native'
  },
  {
    icon: ShieldCheckIcon,
    title: 'Secure & Private',
    description: 'Enhanced security with HTTPS and secure storage for your data',
    category: 'security'
  },
  {
    icon: DevicePhoneMobileIcon,
    title: 'Cross-Platform',
    description: 'Works seamlessly across all your devices - phone, tablet, desktop',
    category: 'native'
  },
  {
    icon: ArrowDownTrayIcon,
    title: 'Small Download',
    description: 'Faster installation than traditional apps with automatic updates',
    category: 'performance'
  },
  {
    icon: MapIcon,
    title: 'Location Services',
    description: 'Get personalized recommendations based on your location',
    category: 'engagement'
  }
];

const EnterprisePWAInstaller: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [showFullscreen, setShowFullscreen] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [installStatus, setInstallStatus] = useState<'idle' | 'installing' | 'installed' | 'error'>('idle');
  const [currentScreenshot, setCurrentScreenshot] = useState(0);
  const [screenshots, setScreenshots] = useState<PWAScreenshot[]>([]);
  const [userInteracted, setUserInteracted] = useState(false);
  const [installationStep, setInstallationStep] = useState(0);
  const [showBenefits, setShowBenefits] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<PWAFeature | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const installPromptRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initializePWAInstaller();
    detectDeviceInfo();
    loadScreenshots();
    trackUserInteraction();
  }, []);

  const initializePWAInstaller = () => {
    // Check if already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
      return;
    }

    // Listen for beforeinstallprompt
    const handleBeforeInstallPrompt = (event: any) => {
      event.preventDefault();
      setDeferredPrompt(event);
      setIsInstallable(true);
      
      // Show install prompt after user interaction
      setTimeout(() => {
        if (shouldShowInstallPrompt()) {
          setShowPrompt(true);
        }
      }, 5000);
    };

    // Listen for appinstalled
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowPrompt(false);
      setShowFullscreen(false);
      setInstallStatus('installed');
      trackInstallation('success');
      
      // Show success message
      showInstallSuccessMessage();
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  };

  const detectDeviceInfo = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform.toLowerCase();
    
    // Detect device type
    let deviceType: DeviceInfo['type'] = 'desktop';
    if (/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      deviceType = /ipad|tablet/i.test(userAgent) ? 'tablet' : 'mobile';
    } else if (/tablet|ipad/i.test(userAgent)) {
      deviceType = 'tablet';
    }

    // Detect OS
    let os: DeviceInfo['os'] = 'unknown';
    if (/ios|iphone|ipad|ipod/i.test(userAgent)) os = 'ios';
    else if (/android/i.test(userAgent)) os = 'android';
    else if (/windows/i.test(userAgent)) os = 'windows';
    else if (/macintosh|mac os x/i.test(userAgent)) os = 'macos';
    else if (/linux/i.test(userAgent)) os = 'linux';

    // Detect browser
    let browser: DeviceInfo['browser'] = 'unknown';
    if (/chrome/i.test(userAgent)) browser = 'chrome';
    else if (/firefox/i.test(userAgent)) browser = 'firefox';
    else if (/safari/i.test(userAgent)) browser = 'safari';
    else if (/edge/i.test(userAgent)) browser = 'edge';

    // Detect capabilities
    const capabilities: string[] = [];
    if ('serviceWorker' in navigator) capabilities.push('Service Worker');
    if ('PushManager' in window) capabilities.push('Push Notifications');
    if ('geolocation' in navigator) capabilities.push('Location Services');
    if ('storage' in navigator) capabilities.push('Persistent Storage');
    if ('share' in navigator) capabilities.push('Web Share API');
    if ('camera' in navigator.mediaDevices) capabilities.push('Camera Access');

    setDeviceInfo({
      type: deviceType,
      os,
      browser,
      capabilities
    });
  };

  const loadScreenshots = async () => {
    try {
      const manifest = await fetch('/manifest.json');
      const manifestData = await manifest.json();
      
      if (manifestData.screenshots) {
        setScreenshots(manifestData.screenshots);
      }
    } catch (error) {
      console.error('Error loading screenshots:', error);
    }
  };

  const trackUserInteraction = () => {
    const handleFirstInteraction = () => {
      setUserInteracted(true);
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };

    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('touchstart', handleFirstInteraction);
  };

  const shouldShowInstallPrompt = () => {
    // Check if user has dismissed recently
    const lastDismissed = localStorage.getItem('pwa-install-dismissed');
    if (lastDismissed) {
      const dismissTime = new Date(lastDismissed);
      const now = new Date();
      const daysSinceDismiss = (now.getTime() - dismissTime.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceDismiss < 7) return false;
    }

    // Check if user has interacted
    if (!userInteracted) return false;

    // Check if already installed
    if (isInstalled) return false;

    // Check page visits
    const visitCount = parseInt(localStorage.getItem('pwa-visit-count') || '0');
    localStorage.setItem('pwa-visit-count', (visitCount + 1).toString());
    
    return visitCount >= 2;
  };

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      setShowFullscreen(true);
      return;
    }

    setInstallStatus('installing');
    setInstallationStep(1);

    try {
      const result = await deferredPrompt.prompt();
      
      if (result.outcome === 'accepted') {
        setInstallStatus('installed');
        setInstallationStep(3);
        trackInstallation('accepted');
        
        setTimeout(() => {
          setShowPrompt(false);
          setShowFullscreen(false);
        }, 2000);
      } else {
        setInstallStatus('idle');
        setInstallationStep(0);
        trackInstallation('declined');
      }
    } catch (error) {
      setInstallStatus('error');
      setInstallationStep(0);
      trackInstallation('error');
      console.error('Installation error:', error);
    }

    setDeferredPrompt(null);
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    setShowFullscreen(false);
    localStorage.setItem('pwa-install-dismissed', new Date().toISOString());
    trackInstallation('dismissed');
  };

  const showInstallSuccessMessage = () => {
    // Create temporary success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2';
    notification.innerHTML = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      <span>BAKASANA installed successfully!</span>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 5000);
  };

  const trackInstallation = (action: string) => {
    if (window.gtag) {
      window.gtag('event', 'pwa_install', {
        event_category: 'PWA',
        event_label: action,
        value: 1,
        custom_parameter_1: deviceInfo?.type,
        custom_parameter_2: deviceInfo?.browser
      });
    }
  };

  const getDeviceSpecificInstructions = () => {
    if (!deviceInfo) return null;

    const instructions = {
      ios: {
        safari: [
          'Tap the Share button at the bottom of the screen',
          'Scroll down and tap "Add to Home Screen"',
          'Tap "Add" to confirm installation'
        ]
      },
      android: {
        chrome: [
          'Tap the menu button (three dots)',
          'Select "Add to Home screen"',
          'Tap "Add" to confirm'
        ]
      },
      windows: {
        chrome: [
          'Click the install button in the address bar',
          'Click "Install" in the popup',
          'The app will be added to your Start menu'
        ],
        edge: [
          'Click the menu button (three dots)',
          'Select "Apps" > "Install this site as an app"',
          'Click "Install" to confirm'
        ]
      },
      macos: {
        safari: [
          'Click "File" in the menu bar',
          'Select "Add to Dock"',
          'The app will be added to your Dock'
        ]
      }
    };

    return instructions[deviceInfo.os]?.[deviceInfo.browser] || instructions.android.chrome;
  };

  const nextScreenshot = () => {
    setCurrentScreenshot((prev) => (prev + 1) % screenshots.length);
  };

  const prevScreenshot = () => {
    setCurrentScreenshot((prev) => (prev - 1 + screenshots.length) % screenshots.length);
  };

  const getInstallButtonText = () => {
    switch (installStatus) {
      case 'installing':
        return 'Installing...';
      case 'installed':
        return 'Installed!';
      case 'error':
        return 'Try Again';
      default:
        return 'Install App';
    }
  };

  const getInstallButtonIcon = () => {
    switch (installStatus) {
      case 'installing':
        return <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />;
      case 'installed':
        return <CheckCircleIcon className="w-4 h-4" />;
      case 'error':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      default:
        return <ArrowDownTrayIcon className="w-4 h-4" />;
    }
  };

  const getDeviceIcon = () => {
    switch (deviceInfo?.type) {
      case 'mobile':
        return <DevicePhoneMobileIcon className="w-8 h-8" />;
      case 'tablet':
        return <DeviceTabletIcon className="w-8 h-8" />;
      default:
        return <ComputerDesktopIcon className="w-8 h-8" />;
    }
  };

  // Don't show if already installed
  if (isInstalled) return null;

  return (
    <>
      {/* Quick Install Banner */}
      <AnimatePresence>
        {showPrompt && !showFullscreen && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-2xl z-50"
          >
            <div className="max-w-4xl mx-auto p-4">
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-enterprise-brown to-enterprise-brown/80 rounded-lg flex items-center justify-center text-white">
                    <SparklesIcon className="w-6 h-6" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <h3 className="font-cormorant text-lg font-medium text-charcoal mb-1">
                    Install BAKASANA Travel App
                  </h3>
                  <p className="text-sm text-gray-600">
                    Get faster access, offline guides, and push notifications for your retreat journey
                  </p>
                </div>
                
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setShowFullscreen(true)}
                    className="text-sm text-enterprise-brown hover:text-enterprise-brown/80 transition-colors"
                  >
                    Learn More
                  </button>
                  
                  <button
                    onClick={handleDismiss}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                  
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleInstallClick}
                    disabled={installStatus === 'installing' || installStatus === 'installed'}
                    className="flex items-center gap-2 px-6 py-3 bg-enterprise-brown text-white rounded-lg hover:bg-enterprise-brown/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {getInstallButtonIcon()}
                    {getInstallButtonText()}
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Fullscreen Install Experience */}
      <AnimatePresence>
        {showFullscreen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-enterprise-brown to-enterprise-brown/80 text-white p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center">
                      <RocketLaunchIcon className="w-8 h-8" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-cormorant font-medium">
                        Install BAKASANA Travel App
                      </h2>
                      <p className="text-white/90 text-sm">
                        Your gateway to transformational yoga retreats
                      </p>
                    </div>
                  </div>
                  
                  <button
                    onClick={handleDismiss}
                    className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <XMarkIcon className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                {/* Installation Steps */}
                {installationStep > 0 && (
                  <div className="mb-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          {[1, 2, 3].map((step) => (
                            <div key={step} className="flex items-center">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                                step <= installationStep 
                                  ? 'bg-enterprise-brown text-white' 
                                  : 'bg-gray-200 text-gray-600'
                              }`}>
                                {step < installationStep ? (
                                  <CheckCircleIcon className="w-4 h-4" />
                                ) : (
                                  step
                                )}
                              </div>
                              {step < 3 && <div className="w-8 h-0.5 bg-gray-200 mx-2" />}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-gray-600">
                        {installationStep === 1 && 'Preparing installation...'}
                        {installationStep === 2 && 'Installing BAKASANA...'}
                        {installationStep === 3 && 'Installation complete!'}
                      </p>
                    </div>
                  </div>
                )}

                {/* Screenshots Carousel */}
                {screenshots.length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-xl font-cormorant font-medium text-charcoal mb-4">
                      App Preview
                    </h3>
                    
                    <div className="relative">
                      <div className="flex overflow-hidden rounded-xl">
                        <motion.div
                          ref={carouselRef}
                          className="flex transition-transform duration-300"
                          style={{ transform: `translateX(-${currentScreenshot * 100}%)` }}
                        >
                          {screenshots.map((screenshot, index) => (
                            <div
                              key={index}
                              className="w-full flex-shrink-0 relative"
                            >
                              <img
                                src={screenshot.src}
                                alt={screenshot.label}
                                className="w-full h-64 object-cover rounded-xl"
                              />
                              <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-3">
                                <p className="text-white font-medium">{screenshot.label}</p>
                              </div>
                            </div>
                          ))}
                        </motion.div>
                      </div>

                      {/* Navigation Buttons */}
                      <button
                        onClick={prevScreenshot}
                        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-colors"
                      >
                        <ChevronLeftIcon className="w-5 h-5" />
                      </button>
                      
                      <button
                        onClick={nextScreenshot}
                        className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-colors"
                      >
                        <ChevronRightIcon className="w-5 h-5" />
                      </button>

                      {/* Dots Indicator */}
                      <div className="flex justify-center gap-2 mt-4">
                        {screenshots.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentScreenshot(index)}
                            className={`w-2 h-2 rounded-full transition-colors ${
                              index === currentScreenshot ? 'bg-enterprise-brown' : 'bg-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Features Grid */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-cormorant font-medium text-charcoal">
                      Why Install?
                    </h3>
                    <button
                      onClick={() => setShowBenefits(!showBenefits)}
                      className="text-enterprise-brown hover:text-enterprise-brown/80 transition-colors"
                    >
                      {showBenefits ? 'Show Less' : 'View All Benefits'}
                    </button>
                  </div>
                  
                  <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${showBenefits ? 'md:grid-cols-3' : ''}`}>
                    {(showBenefits ? PWA_FEATURES : PWA_FEATURES.slice(0, 4)).map((feature, index) => (
                      <motion.div
                        key={index}
                        whileHover={{ scale: 1.02 }}
                        onClick={() => setSelectedFeature(feature)}
                        className="p-4 border border-gray-200 rounded-xl hover:shadow-md transition-all cursor-pointer"
                      >
                        <div className="flex items-start gap-3">
                          <div className="w-10 h-10 bg-enterprise-brown/10 rounded-lg flex items-center justify-center flex-shrink-0">
                            <feature.icon className="w-5 h-5 text-enterprise-brown" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-charcoal mb-1">{feature.title}</h4>
                            <p className="text-sm text-gray-600">{feature.description}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Device Info */}
                {deviceInfo && (
                  <div className="mb-8">
                    <h3 className="text-xl font-cormorant font-medium text-charcoal mb-4">
                      Your Device
                    </h3>
                    
                    <div className="bg-gray-50 rounded-xl p-4">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="text-enterprise-brown">
                          {getDeviceIcon()}
                        </div>
                        <div>
                          <p className="font-medium text-charcoal capitalize">
                            {deviceInfo.type} • {deviceInfo.os} • {deviceInfo.browser}
                          </p>
                          <p className="text-sm text-gray-600">
                            {deviceInfo.capabilities.length} advanced features supported
                          </p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2">
                        {deviceInfo.capabilities.map((capability, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <CheckCircleIcon className="w-4 h-4 text-green-600" />
                            <span className="text-sm text-gray-700">{capability}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Installation Instructions */}
                {!deferredPrompt && (
                  <div className="mb-8">
                    <h3 className="text-xl font-cormorant font-medium text-charcoal mb-4">
                      Installation Guide
                    </h3>
                    
                    <div className="bg-blue-50 rounded-xl p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <InformationCircleIcon className="w-5 h-5 text-blue-600" />
                        <span className="font-medium text-blue-900">How to install on your device:</span>
                      </div>
                      
                      <ol className="space-y-2">
                        {getDeviceSpecificInstructions()?.map((instruction, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                              {index + 1}
                            </span>
                            <span className="text-blue-900">{instruction}</span>
                          </li>
                        ))}
                      </ol>
                    </div>
                  </div>
                )}

                {/* Install Button */}
                <div className="text-center">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleInstallClick}
                    disabled={installStatus === 'installing' || installStatus === 'installed'}
                    className="inline-flex items-center gap-3 px-8 py-4 bg-enterprise-brown text-white rounded-xl font-medium hover:bg-enterprise-brown/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {getInstallButtonIcon()}
                    {getInstallButtonText()}
                  </motion.button>
                  
                  {!deferredPrompt && (
                    <p className="text-sm text-gray-600 mt-3">
                      Follow the instructions above to install manually
                    </p>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Feature Detail Modal */}
      <AnimatePresence>
        {selectedFeature && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedFeature(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl max-w-md w-full p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-enterprise-brown/10 rounded-xl flex items-center justify-center">
                  <selectedFeature.icon className="w-6 h-6 text-enterprise-brown" />
                </div>
                <div>
                  <h3 className="text-xl font-cormorant font-medium text-charcoal">
                    {selectedFeature.title}
                  </h3>
                  <span className="text-sm text-gray-500 capitalize">{selectedFeature.category}</span>
                </div>
              </div>
              
              <p className="text-gray-700 mb-6">{selectedFeature.description}</p>
              
              <button
                onClick={() => setSelectedFeature(null)}
                className="w-full py-3 bg-enterprise-brown text-white rounded-lg hover:bg-enterprise-brown/90 transition-colors"
              >
                Got it!
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default EnterprisePWAInstaller;

// Extend window interface for TypeScript
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}